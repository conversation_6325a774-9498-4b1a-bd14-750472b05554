import React from 'react'
import Navbar from './components/Navbar.jsx'
import Hero from './components/Hero.jsx'
import About from './components/About.jsx'
import Featured from './components/Featured.jsx'
import Experience from './components/Experience.jsx'
import Skills from './components/Skills.jsx'
import Education from './components/Education.jsx'
import Publications from './components/Publications.jsx'

import Footer from './components/Footer.jsx'
import { StagewiseToolbar } from '@stagewise/toolbar-react';
import { ReactPlugin } from '@stagewise-plugins/react';

function App() {
  return (
    <div className="App">
      <StagewiseToolbar config={{ plugins: [ReactPlugin] }} />
      {/* Skip link for accessibility */}
      <a href="#main-content" className="skip-link">
        Skip to main content
      </a>

      <Navbar />

      <main id="main-content" role="main" aria-label="Main content">
        <Hero />
        <About />
        <Featured />
        <Experience />
        <Skills />
        <Education />
        <Publications />
      </main>

      <Footer />
    </div>
  )
}

export default App
