import React from 'react';
import FadeInUp from './FadeInUp.jsx';
import resumeData from '../services/resumeData.jsx';
import './Featured.css';

const Featured = () => {
  const certificates = resumeData.getCertificates();



  // Featured content based on LinkedIn recommendations
  const featuredItems = [
    {
      type: 'achievement',
      title: 'Red Hat Innovation Award Winner',
      description: 'Recognized for outstanding achievements in open source with FICO\'s $10M+ analytics platform',
      icon: '🏆',
      link: '#awards',
      highlight: 'Reduced infrastructure costs by 20% and improved time to market by 70%'
    },
    {
      type: 'certification',
      title: 'AWS Solutions Architect',
      description: 'Current AWS certification demonstrating cloud architecture expertise',
      icon: '☁️',
      link: certificates.find(cert => cert.name.includes('AWS'))?.url || '#certificates',
      highlight: 'Valid through 2025'
    },
    {
      type: 'expertise',
      title: 'AI/ML Integration Specialist',
      description: 'Leading AI-driven solutions and automation implementations',
      icon: '🤖',
      link: '#experience',
      highlight: 'Implementing cutting-edge AI solutions at AHEAD'
    },
    {
      type: 'thought-leadership',
      title: 'OpenStack Speakers Bureau',
      description: 'Published articles and industry conference presentations',
      icon: '🎤',
      link: '#publications',
      highlight: 'Recognized thought leader in cloud technologies'
    }
  ];

  return (
    <section id="featured" className="featured" aria-labelledby="featured-title">
      <div className="container">
        <FadeInUp>
          <h2 id="featured-title" className="section-title">Featured Highlights</h2>
          <p className="section-subtitle">Key achievements and expertise that drive results</p>
        </FadeInUp>
        
        <div className="featured-grid">
          {featuredItems.map((item, index) => (
            <FadeInUp key={index} delay={0.1 * (index + 1)}>
              <div className="featured-card">
                <div className="featured-card-header">
                  <span className="featured-icon">{item.icon}</span>
                  <div className="featured-badge">{item.type}</div>
                </div>
                <h3 className="featured-title">{item.title}</h3>
                <p className="featured-description">{item.description}</p>
              </div>
            </FadeInUp>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Featured;