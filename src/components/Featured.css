.featured {
  background-color: var(--color-background);
  padding: var(--space-32) 0;
}

.container {
  max-width: var(--container-lg);
  margin: 0 auto;
  padding: 0 var(--space-24);
}

.section-title {
  color: var(--color-text);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  text-align: center;
  margin-bottom: var(--space-8);
}

.section-subtitle {
  color: var(--color-text-secondary);
  font-size: var(--font-size-lg);
  text-align: center;
  margin-bottom: var(--space-32);
}

.featured-grid {
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: minmax(280px, 1fr);
  gap: var(--space-24);
  margin-bottom: var(--space-48);
  overflow-x: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--color-primary) transparent;
  padding-bottom: var(--space-16);
}

/* Custom scrollbar for WebKit browsers */
.featured-grid::-webkit-scrollbar {
  height: 8px;
}

.featured-grid::-webkit-scrollbar-track {
  background: transparent;
}

.featured-grid::-webkit-scrollbar-thumb {
  background-color: var(--color-primary);
  border-radius: 4px;
}

.featured-card {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  transition: transform var(--duration-normal) var(--ease-standard);
}

.featured-card:hover {
  transform: translateY(-4px);
  border-color: var(--color-primary);
}

.featured-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-16);
}

.featured-icon {
  font-size: var(--font-size-2xl);
}

.featured-badge {
  background-color: var(--color-primary);
  color: var(--color-text);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  text-transform: capitalize;
}

.featured-title {
  color: var(--color-text);
  font-size: var(--font-size-xl);
  margin-bottom: var(--space-8);
}

.featured-description {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
  margin-bottom: var(--space-16);
}

.featured-highlight {
  background-color: var(--color-primary);
  border-radius: var(--radius-base);
  padding: var(--space-12);
  margin-bottom: var(--space-16);
}

.highlight-text {
  color: var(--color-text);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.featured-link {
  color: var(--color-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  transition: color var(--duration-normal) var(--ease-standard);
}

.featured-link:hover {
  color: var(--color-primary-hover);
}

@media (max-width: 768px) {
  .featured-grid {
    grid-template-columns: 1fr;
  }
}